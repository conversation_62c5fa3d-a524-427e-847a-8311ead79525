"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Outlet, User, Dish } from "@/app/type";
import {
  getAllOutlets,
  getAllCustomers,
  getAllDishes,
  createManualOrder,
  createCustomer,
  validateCouponForOrder,
} from "@/server/admin";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  Trash2,
  UserPlus,
  User as UserIcon,
  Tag,
  X,
  Mail,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";

interface CreateOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export default function CreateOrderDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateOrderDialogProps) {
  const [loading, setLoading] = useState(false);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [customers, setCustomers] = useState<User[]>([]);
  const [allDishes, setAllDishes] = useState<Dish[]>([]);
  const [filteredDishes, setFilteredDishes] = useState<Dish[]>([]);
  const [selectedOutlet, setSelectedOutlet] = useState<string>("");
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<"cash" | "online">("cash");
  const [specialInstructions, setSpecialInstructions] = useState<string>("");
  const [tableNumber, setTableNumber] = useState<string>("");
  const [orderItems, setOrderItems] = useState<
    { dishId: string; quantity: number }[]
  >([]);
  const [dishSearchTerm, setDishSearchTerm] = useState<string>("");
  const [customerSearchTerm, setCustomerSearchTerm] = useState<string>("");
  const [filteredCustomers, setFilteredCustomers] = useState<User[]>([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState<boolean>(false);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  // Coupon related state
  const [couponCode, setCouponCode] = useState<string>("");
  const [validatingCoupon, setValidatingCoupon] = useState<boolean>(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: "percentage" | "fixed";
    discountValue: number;
  } | null>(null);
  const [finalAmount, setFinalAmount] = useState<number>(0);
  const [sendInvoiceEmail, setSendInvoiceEmail] = useState<boolean>(true);

  // New customer form state
  const [showNewCustomerForm, setShowNewCustomerForm] =
    useState<boolean>(false);
  const [newCustomerName, setNewCustomerName] = useState<string>("");
  const [newCustomerEmail, setNewCustomerEmail] = useState<string>("");
  const [newCustomerPhone, setNewCustomerPhone] = useState<string>("");
  const [creatingCustomer, setCreatingCustomer] = useState<boolean>(false);
  // We need to store the password temporarily but don't display it in the UI
  // It's shown in toast notifications instead
  const [, setNewCustomerPassword] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      console.log("Dialog opened, fetching initial data");
      fetchInitialData();
    }
  }, [open]);

  // Debug log for customers
  useEffect(() => {
    console.log("Customers state updated:", customers);
  }, [customers]);

  useEffect(() => {
    if (selectedOutlet) {
      fetchOutletDishes(selectedOutlet);
    }
  }, [selectedOutlet]);

  useEffect(() => {
    // Filter dishes based on search term
    if (allDishes.length > 0) {
      const filtered = allDishes.filter((dish) =>
        dish.name.toLowerCase().includes(dishSearchTerm.toLowerCase())
      );
      setFilteredDishes(filtered);
    }
  }, [dishSearchTerm, allDishes]);

  useEffect(() => {
    // Calculate total amount
    let total = 0;
    orderItems.forEach((item) => {
      const dish = allDishes.find((d) => d._id === item.dishId);
      if (dish) {
        total += dish.price * item.quantity;
      }
    });
    setTotalAmount(total);

    // Reset applied coupon if items change
    if (appliedCoupon) {
      setAppliedCoupon(null);
      setCouponCode("");
    }
  }, [orderItems, allDishes, appliedCoupon]);

  // Update final amount when total amount or applied coupon changes
  useEffect(() => {
    if (appliedCoupon) {
      setFinalAmount(totalAmount - appliedCoupon.discount);
    } else {
      setFinalAmount(totalAmount);
    }
  }, [totalAmount, appliedCoupon]);

  const fetchInitialData = async () => {
    try {
      // Fetch outlets
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      } else {
        toast.error("Failed to load outlets");
      }

      // Fetch customers - load more customers initially for better selection
      const customersResponse = await getAllCustomers({ page: 1, limit: 100 });
      console.log("Raw customer response:", customersResponse);

      if (customersResponse && customersResponse.success) {
        // Handle different response formats
        if (customersResponse.data && customersResponse.data.customers) {
          setCustomers(customersResponse.data.customers);
        } else if (Array.isArray(customersResponse.data)) {
          setCustomers(customersResponse.data);
        } else {
          console.error(
            "Unexpected customer data format:",
            customersResponse.data
          );
          setCustomers([]);
        }
      } else {
        console.error("Failed to load customers:", customersResponse);
        toast.error("Failed to load customers");
        setCustomers([]);
      }

      // Reset search states
      setCustomerSearchTerm("");
      setFilteredCustomers([]);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      toast.error("Failed to load initial data");
    }
  };

  const fetchOutletDishes = async (outletId: string) => {
    try {
      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        // Filter dishes that are available in the selected outlet
        const outletDishes = dishesResponse.data.filter(
          (dish: Dish) => dish.outlets.includes(outletId) && dish.isAvailable
        );
        setAllDishes(outletDishes);
        setFilteredDishes(outletDishes);
      } else {
        toast.error("Failed to load dishes");
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  const handleAddDish = (dishId: string) => {
    const existingItem = orderItems.find((item) => item.dishId === dishId);
    if (existingItem) {
      // Increment quantity if dish already exists in order
      setOrderItems(
        orderItems.map((item) =>
          item.dishId === dishId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      // Add new dish to order
      setOrderItems([...orderItems, { dishId, quantity: 1 }]);
    }
  };

  const handleRemoveDish = (dishId: string) => {
    setOrderItems(orderItems.filter((item) => item.dishId !== dishId));
  };

  const handleQuantityChange = (dishId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      handleRemoveDish(dishId);
      return;
    }

    setOrderItems(
      orderItems.map((item) =>
        item.dishId === dishId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const resetForm = () => {
    setSelectedOutlet("");
    setSelectedCustomer("");
    setPaymentMethod("cash");
    setSpecialInstructions("");
    setTableNumber("");
    setOrderItems([]);
    setDishSearchTerm("");
    setCustomerSearchTerm("");
    setAllDishes([]);
    setFilteredDishes([]);
    setShowNewCustomerForm(false);
    setNewCustomerName("");
    setNewCustomerEmail("");
    setNewCustomerPhone("");
    setCouponCode("");
    setAppliedCoupon(null);
    setSendInvoiceEmail(true);
  };

  // Function to validate and apply coupon
  const handleValidateCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    if (!selectedOutlet) {
      toast.error("Please select an outlet first");
      return;
    }

    if (totalAmount <= 0) {
      toast.error("Please add items to the order first");
      return;
    }

    setValidatingCoupon(true);
    try {
      const response = await validateCouponForOrder({
        code: couponCode,
        outletId: selectedOutlet,
        amount: totalAmount,
      });

      if (response.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: response.data.discount,
          discountType: response.data.coupon.discountType,
          discountValue: response.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
      } else {
        toast.error(response.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error validating coupon:", error);
      toast.error("An error occurred while validating the coupon");
    } finally {
      setValidatingCoupon(false);
    }
  };

  // Function to remove applied coupon
  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
    setCouponCode("");
  };

  // Function to search for customers
  const searchCustomers = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      return;
    }

    setIsLoadingCustomers(true);
    try {
      const response = await getAllCustomers({
        page: 1,
        limit: 10,
        search: searchTerm,
      });
      if (response.success) {
        // Handle different response formats
        if (response.data && response.data.customers) {
          setFilteredCustomers(response.data.customers);
        } else if (Array.isArray(response.data)) {
          setFilteredCustomers(response.data);
        } else {
          console.error("Unexpected customer data format:", response.data);
          setFilteredCustomers([]);
        }
      } else {
        console.error("Failed to search customers:", response);
        setFilteredCustomers([]);
      }
    } catch (error) {
      console.error("Error searching customers:", error);
      setFilteredCustomers([]);
    } finally {
      setIsLoadingCustomers(false);
    }
  };

  const handleCreateCustomer = async () => {
    // Validate form
    if (!newCustomerName) {
      toast.error("Please enter customer name");
      return;
    }

    if (!newCustomerEmail) {
      toast.error("Please enter customer email");
      return;
    }

    setCreatingCustomer(true);
    try {
      const response = await createCustomer({
        name: newCustomerName,
        email: newCustomerEmail,
        phone: newCustomerPhone,
      });

      if (response.success) {
        // Store the initial password if provided
        if (response.data.initialPassword) {
          setNewCustomerPassword(response.data.initialPassword);

          // Show a toast with instructions
          toast.success(
            "Customer created successfully. Please note down the temporary password: " +
              response.data.initialPassword
          );
        } else {
          toast.success("Customer created successfully");
        }

        // Add the new customer to the customers list
        const newCustomer = response.data;
        setCustomers([...customers, newCustomer]);

        // Select the new customer
        setSelectedCustomer(newCustomer._id);

        // Reset the form
        setShowNewCustomerForm(false);
        setNewCustomerName("");
        setNewCustomerEmail("");
        setNewCustomerPhone("");
      } else {
        toast.error(response.message || "Failed to create customer");
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("An error occurred while creating the customer");
    } finally {
      setCreatingCustomer(false);
    }
  };

  const handleSubmit = async () => {
    // Validate form
    if (!selectedOutlet) {
      toast.error("Please select an outlet");
      return;
    }

    if (
      (!selectedCustomer || selectedCustomer === "no-id-placeholder") &&
      !showNewCustomerForm
    ) {
      toast.error("Please select a valid customer");
      return;
    }

    if (showNewCustomerForm) {
      // Create customer first
      if (!newCustomerName || !newCustomerEmail) {
        toast.error("Please fill in customer details");
        return;
      }

      // Create the customer and then proceed with order creation
      setCreatingCustomer(true);
      try {
        const customerResponse = await createCustomer({
          name: newCustomerName,
          email: newCustomerEmail,
          phone: newCustomerPhone,
        });

        if (!customerResponse.success) {
          toast.error(customerResponse.message || "Failed to create customer");
          setCreatingCustomer(false);
          return;
        }

        // Store the initial password if provided
        if (customerResponse.data.initialPassword) {
          setNewCustomerPassword(customerResponse.data.initialPassword);

          // Show a toast with instructions
          toast.success(
            "Customer created successfully. Please note down the temporary password: " +
              customerResponse.data.initialPassword
          );
        }

        // Set the newly created customer as the selected customer
        setSelectedCustomer(customerResponse.data._id);
        setCreatingCustomer(false);
      } catch (error) {
        console.error("Error creating customer:", error);
        toast.error("An error occurred while creating the customer");
        setCreatingCustomer(false);
        return;
      }
    }

    if (orderItems.length === 0) {
      toast.error("Please add at least one dish to the order");
      return;
    }

    setLoading(true);
    try {
      const orderPayload: {
        outletId: string;
        customerId: string;
        items: { dishId: string; quantity: number }[];
        specialInstructions?: string;
        tableNumber?: string;
        paymentMethod?: "cash" | "online";
        sendInvoiceEmail?: boolean;
        couponCode?: string;
        couponDiscount?: number;
      } = {
        outletId: selectedOutlet,
        customerId: selectedCustomer,
        items: orderItems,
        specialInstructions,
        tableNumber,
        paymentMethod,
        sendInvoiceEmail,
      };

      // Add coupon information if a coupon is applied
      if (appliedCoupon) {
        orderPayload.couponCode = appliedCoupon.code;
        orderPayload.couponDiscount = appliedCoupon.discount;
      }

      const response = await createManualOrder(orderPayload);

      if (response.success) {
        let successMessage = "Order created successfully";
        if (sendInvoiceEmail) {
          successMessage += ". Invoice has been sent to the customer's email.";
        }
        toast.success(successMessage);
        resetForm();
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to create order");
      }
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("An error occurred while creating the order");
    } finally {
      setLoading(false);
    }
  };

  const formatOutletDisplay = (outlet: Outlet) => {
    return `${outlet.name} (${outlet.address.split(",")[0]})`;
  };

  const getDishById = (dishId: string) => {
    return allDishes.find((dish) => dish._id === dishId);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Order</DialogTitle>
          <DialogDescription>
            Create a manual order for a customer
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Outlet Selection */}
          <div className="grid gap-2">
            <Label htmlFor="outlet">Select Outlet</Label>
            <Select value={selectedOutlet} onValueChange={setSelectedOutlet}>
              <SelectTrigger>
                <SelectValue placeholder="Select an outlet" />
              </SelectTrigger>
              <SelectContent>
                {outlets.map((outlet) => (
                  <SelectItem key={outlet._id} value={outlet._id || ""}>
                    {formatOutletDisplay(outlet)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Customer Selection */}
          <div className="grid gap-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="customer">Select Customer</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowNewCustomerForm(!showNewCustomerForm)}
                className="text-xs flex items-center gap-1"
              >
                {showNewCustomerForm ? (
                  <>
                    <UserIcon className="h-3 w-3" /> Select Existing
                  </>
                ) : (
                  <>
                    <UserPlus className="h-3 w-3" /> Create New
                  </>
                )}
              </Button>
            </div>

            {!showNewCustomerForm ? (
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    placeholder="Search customers by name, email, or phone"
                    value={customerSearchTerm}
                    onChange={(e) => {
                      const value = e.target.value;
                      setCustomerSearchTerm(value);
                      if (value.length >= 2) {
                        searchCustomers(value);
                      } else if (value.length === 0) {
                        // Reset to all customers when search is cleared
                        setFilteredCustomers([]);
                      }
                    }}
                  />
                  {isLoadingCustomers && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>

                {customerSearchTerm.length >= 2 ? (
                  <div className="border rounded-md max-h-[200px] overflow-y-auto">
                    {filteredCustomers.length > 0 ? (
                      filteredCustomers.map((customer) => (
                        <div
                          key={customer._id}
                          className={`p-2 hover:bg-gray-100 cursor-pointer ${
                            selectedCustomer === customer._id
                              ? "bg-gray-100"
                              : ""
                          }`}
                          onClick={() =>
                            setSelectedCustomer(customer._id || "")
                          }
                        >
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-sm text-gray-500 flex gap-2">
                            <span>{customer.email}</span>
                            {customer.phone && <span>• {customer.phone}</span>}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-3 text-center text-gray-500">
                        No customers found matching your search
                      </div>
                    )}
                  </div>
                ) : (
                  <Select
                    value={selectedCustomer}
                    onValueChange={setSelectedCustomer}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(customers) && customers.length > 0 ? (
                        customers.map((customer) => (
                          <SelectItem
                            key={customer._id}
                            value={customer._id || "no-id-placeholder"}
                          >
                            {customer.name}{" "}
                            {customer.phone && `(${customer.phone})`}
                          </SelectItem>
                        ))
                      ) : (
                        <div className="p-2 text-center text-gray-500">
                          No customers found
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                )}

                {selectedCustomer && (
                  <div className="text-sm text-gray-500">
                    {customers.find((c) => c._id === selectedCustomer)?.name ||
                      "Selected customer"}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-3 border p-3 rounded-md">
                <div className="grid gap-2">
                  <Label htmlFor="newCustomerName">Name</Label>
                  <Input
                    id="newCustomerName"
                    value={newCustomerName}
                    onChange={(e) => setNewCustomerName(e.target.value)}
                    placeholder="Enter customer name"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="newCustomerEmail">Email</Label>
                  <Input
                    id="newCustomerEmail"
                    type="email"
                    value={newCustomerEmail}
                    onChange={(e) => setNewCustomerEmail(e.target.value)}
                    placeholder="Enter customer email"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="newCustomerPhone">Phone (optional)</Label>
                  <Input
                    id="newCustomerPhone"
                    value={newCustomerPhone}
                    onChange={(e) => setNewCustomerPhone(e.target.value)}
                    placeholder="Enter customer phone"
                  />
                </div>
                <Button
                  type="button"
                  onClick={handleCreateCustomer}
                  disabled={
                    creatingCustomer || !newCustomerName || !newCustomerEmail
                  }
                  className="w-full"
                >
                  {creatingCustomer ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Customer"
                  )}
                </Button>
              </div>
            )}
          </div>

          {/* Payment Method */}
          <div className="grid gap-2">
            <Label className="text-base">Payment Method</Label>
            <RadioGroup
              value={paymentMethod}
              onValueChange={(value) =>
                setPaymentMethod(value as "cash" | "online")
              }
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cash" id="cash" />
                <Label htmlFor="cash">Cash</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="online" id="online" />
                <Label htmlFor="online">Online</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Coupon Code */}
          <div className="grid gap-2">
            <Label htmlFor="couponCode">Coupon Code</Label>
            <div className="flex space-x-2">
              <Input
                id="couponCode"
                placeholder="Enter coupon code"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
                disabled={!!appliedCoupon || validatingCoupon}
                className="flex-1"
              />
              {appliedCoupon ? (
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleRemoveCoupon}
                  className="shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleValidateCoupon}
                  disabled={!couponCode.trim() || validatingCoupon}
                  className="shrink-0"
                >
                  {validatingCoupon ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Tag className="h-4 w-4 mr-2" />
                  )}
                  Apply
                </Button>
              )}
            </div>
            {appliedCoupon && (
              <div className="text-sm bg-green-50 p-2 rounded border border-green-200">
                <p className="font-medium text-green-700">
                  Coupon {appliedCoupon.code} applied!
                </p>
                <p className="text-green-600">
                  {appliedCoupon.discountType === "percentage"
                    ? `${appliedCoupon.discountValue}% off`
                    : `₹${appliedCoupon.discountValue} off`}
                  {" - "}
                  Discount: ₹{appliedCoupon.discount.toFixed(2)}
                </p>
              </div>
            )}
          </div>

          {/* Send Invoice Email */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendInvoiceEmail"
              checked={sendInvoiceEmail}
              onCheckedChange={(checked) =>
                setSendInvoiceEmail(checked as boolean)
              }
            />
            <Label htmlFor="sendInvoiceEmail" className="flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Send invoice to customer&apos;s email after order completion
            </Label>
          </div>

          {/* Table Number */}
          <div className="grid gap-2">
            <Label htmlFor="tableNumber">Table Number (Optional)</Label>
            <Input
              id="tableNumber"
              placeholder="Enter table number"
              value={tableNumber}
              onChange={(e) => setTableNumber(e.target.value)}
            />
          </div>

          {/* Special Instructions */}
          <div className="grid gap-2">
            <Label htmlFor="specialInstructions">Special Instructions</Label>
            <Textarea
              id="specialInstructions"
              placeholder="Add any special instructions for this order"
              value={specialInstructions}
              onChange={(e) => setSpecialInstructions(e.target.value)}
            />
          </div>

          {/* Dish Selection */}
          {selectedOutlet && (
            <div className="grid gap-4 mt-4">
              <div className="flex justify-between items-center">
                <Label className="text-base">Add Dishes</Label>
                <div className="text-sm font-medium">
                  {appliedCoupon ? (
                    <div className="text-right">
                      <div className="text-gray-500 line-through">
                        ₹{totalAmount.toFixed(2)}
                      </div>
                      <div className="text-green-600">
                        Final: ₹{finalAmount.toFixed(2)}
                      </div>
                    </div>
                  ) : (
                    <div>Total: ₹{totalAmount.toFixed(2)}</div>
                  )}
                </div>
              </div>

              {/* Search Box */}
              <Input
                placeholder="Search dishes..."
                value={dishSearchTerm}
                onChange={(e) => setDishSearchTerm(e.target.value)}
                className="mb-2"
              />

              {/* Selected Dishes */}
              {orderItems.length > 0 && (
                <div className="border rounded-md p-3 mb-4 bg-gray-50">
                  <h4 className="font-medium mb-2">Selected Items</h4>
                  <div className="space-y-2">
                    {orderItems.map((item) => {
                      const dish = getDishById(item.dishId);
                      return dish ? (
                        <div
                          key={item.dishId}
                          className="flex justify-between items-center"
                        >
                          <div className="flex-1">
                            <p className="font-medium">{dish.name}</p>
                            <p className="text-sm text-gray-500">
                              ₹{dish.price.toFixed(2)} each
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() =>
                                handleQuantityChange(
                                  item.dishId,
                                  item.quantity - 1
                                )
                              }
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center">
                              {item.quantity}
                            </span>
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() =>
                                handleQuantityChange(
                                  item.dishId,
                                  item.quantity + 1
                                )
                              }
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-red-500"
                              onClick={() => handleRemoveDish(item.dishId)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              )}

              {/* Available Dishes */}
              <div className="border rounded-md p-3 max-h-[300px] overflow-y-auto">
                <h4 className="font-medium mb-2">Available Dishes</h4>
                {filteredDishes.length === 0 ? (
                  <p className="text-sm text-gray-500">
                    No dishes available for this outlet
                  </p>
                ) : (
                  <div className="grid grid-cols-1 gap-2">
                    {filteredDishes.map((dish) => (
                      <div
                        key={dish._id}
                        className="flex justify-between items-center p-2 hover:bg-gray-100 rounded-md"
                      >
                        <div>
                          <p className="font-medium">{dish.name}</p>
                          <p className="text-sm text-gray-500">
                            ₹{dish.price.toFixed(2)}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddDish(dish._id || "")}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              loading ||
              creatingCustomer ||
              !selectedOutlet ||
              ((!selectedCustomer ||
                selectedCustomer === "no-id-placeholder") &&
                !showNewCustomerForm) ||
              (showNewCustomerForm &&
                (!newCustomerName || !newCustomerEmail)) ||
              orderItems.length === 0
            }
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Order"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
